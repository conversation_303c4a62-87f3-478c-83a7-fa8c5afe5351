@php
    // The $record variable is passed in from the PDF controller.
    $letterSetting = $record->letterSetting;

    // Fetch ISO certifications
    $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
        ->whereIn('name', $isoNamesToDisplay)
        ->get();
@endphp
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>SPH - {{ $record->sph_number }}</title>
    <style>
        /* This CSS is optimized for PDF generation with DomPDF */
        @page { margin: 20px; }
        body { font-family: 'Helvetica', Arial, sans-serif; font-size: 11px; color: #333; }
        table { width: 100%; border-collapse: collapse; }
        .header, .footer { width: 100%; }
        .header td, .footer td { vertical-align: top; padding: 0; }
        .logo { max-height: 50px; }
        .text-right { text-align: right; }
        .font-bold { font-weight: bold; }
        .align-top { vertical-align: top; }
        .price-table { margin-top: 1rem; }
        .price-table th, .price-table td { border: 1px solid #333; padding: 5px; }
        .price-table th { background-color: #f2f2f2; }
        .price-table .text-right { text-align: right; }
        .price-table .text-center { text-align: center; }
        .signature { margin-top: 50px; text-align: right; }
        .signature-space { height: 60px; }
        .footer { position: fixed; bottom: 0; left: 0; right: 0; border-top: 3px solid #003366; padding-top: 10px; font-size: 9px; }
        .iso-logos img { height: 35px; margin-right: 5px; }
    </style>
</head>
<body>

    {{-- Header Section --}}
    <table class="header">
        <tr>
            <td style="width: 40%;">
                @php $logoPath = public_path('storage/business-logos/lrp-colored.png'); @endphp
                @if(file_exists($logoPath))
                    <img src="data:image/png;base64,{{ base64_encode(file_get_contents($logoPath)) }}" alt="Company Logo" class="logo">
                @endif
            </td>
            <td style="width: 60%;" class="text-right">
                <h2 style="font-size: 14px; margin: 0;">TRUSTED & RELIABLE PARTNER</h2>
                <p style="font-size: 9px; margin: 0;">Fuel Agent – Fuel Transportation – Bunker Service</p>
            </td>
        </tr>
    </table>

    {{-- Document Info Section --}}
    <div style="margin-bottom: 2rem;">
        <div style="text-align: right; font-size: 11px; margin-bottom: 1rem;">
            {{ $letterSetting?->city ?? 'Pekanbaru' }}, {{ $record->sph_date->format('F j, Y') }}
        </div>
        <table style="font-size: 11px;">
            <tr>
                <td style="width: 80px;">No.</td>
                <td style="width: 10px;">:</td>
                <td class="font-bold">{{ $record->sph_number }}</td>
            </tr>
            <tr>
                <td>Attachment</td>
                <td>:</td>
                <td>-</td>
            </tr>
            <tr>
                <td class="align-top">Subject</td>
                <td class="align-top">:</td>
                <td class="font-bold">Price Quotation</td>
            </tr>
        </table>
    </div>

    {{-- Recipient Section --}}
    <div style="margin-bottom: 2rem; font-size: 11px;">
        <p>To:</p>
        <p class="font-bold">{{ $record->customer?->nama }}</p>
        @if($record->opsional_pic)
            <p>Attn: {{ $record->opsional_pic }}</p>
        @elseif($record->customer?->pic_nama)
             <p>Attn: {{ $record->customer->pic_nama }}</p>
        @endif
        <p>At your location</p>
    </div>

    {{-- Body / Salutation --}}
    <div style="margin-bottom: 1.5rem; font-size: 11px; line-height: 1.5;">
        <p style="margin-bottom: 1rem;">Dear Sir/Madam,</p>
        <p>
            In connection with the information on fuel needs, we hereby send a price quotation for the period
            <span class="font-bold">{{ $record->sph_date->format('F j, Y') }}</span> to <span class="font-bold">{{ $record->valid_until_date->format('F j, Y') }}</span>.
        </p>
    </div>

    {{-- Price Details Table --}}
    <div style="margin-bottom: 2rem;">
        <h2 style="font-size: 13px; margin-bottom: 0.5rem;">The price quotation we provide is as follows:</h2>
        <table class="price-table">
            <thead>
                <tr>
                    <th style="width: 40px;">No.</th>
                    <th>Details</th>
                    <th style="width: 120px;">Price/Liter</th>
                </tr>
            </thead>
            <tbody>
                @foreach($record->details as $idx => $detail)
                    <tr>
                        <td class="text-center">{{ $idx + 1 }}</td>
                        <td>Base Fuel Price</td>
                        <td class="text-right">{{ number_format($detail->harga_dasar, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>VAT 11%</td>
                        <td class="text-right">{{ number_format($detail->ppn, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>OAT</td>
                        <td class="text-right">{{ number_format($detail->oat, 0, ',', '.') }}</td>
                    </tr>
                    <tr class="font-bold" style="background-color: #f2f2f2;">
                        <td colspan="2">Total Quotation</td>
                        <td class="text-right">{{ number_format($detail->price, 0, ',', '.') }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    {{-- Signature Section --}}
    <div class="signature">
        <div style="display: inline-block; text-align: center;">
            <p>Sincerely,</p>
            <p>PT Lintas Riau Prima</p>
            <div class="signature-space"></div>
            <p class="font-bold" style="text-decoration: underline;">{{ $record->createdBy?->name ?? 'N/A' }}</p>
            <p style="font-size: 10px;">{{ $record->createdBy?->position ?? 'Marketing Manager' }}</p>
        </div>
    </div>

    {{-- Footer Section --}}
    <table class="footer">
        <tr>
            <td style="width: 33%;" class="iso-logos">
                @foreach ($isoCertifications as $cert)
                    @php $isoLogoPath = public_path('storage/' . $cert->logo_path); @endphp
                    @if(file_exists($isoLogoPath))
                        <img src="data:image/png;base64,{{ base64_encode(file_get_contents($isoLogoPath)) }}" alt="{{ $cert->name }}">
                    @endif
                @endforeach
            </td>
            <td style="width: 34%; text-align: center;">
                <p class="font-bold">PT. LINTAS RIAU PRIMA</p>
                <p>{{ $letterSetting?->address }}</p>
            </td>
            <td style="width: 33%; text-align: left; padding-left: 20px;">
                <p>☎️ {{ $letterSetting?->phone_number }}</p>
                <p>✉️ {{ $letterSetting?->email }}</p>
                <p>🌐 {{ $letterSetting?->website }}</p>
            </td>
        </tr>
    </table>

</body>
</html>
