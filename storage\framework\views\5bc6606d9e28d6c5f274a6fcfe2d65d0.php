<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery Order - <?php echo e($record->kode); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            margin: 20px;
        }

        .header {
            width: 100%;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }

        .header-content {
            display: table;
            width: 100%;
        }

        .logo-section {
            display: table-cell;
            width: 25%;
            vertical-align: top;
        }

        .company-info {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            text-align: center;
            padding: 0 10px;
        }

        .recipient-info {
            display: table-cell;
            width: 25%;
            vertical-align: top;
            text-align: right;
        }

        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .company-tagline {
            font-size: 9px;
            margin-bottom: 1px;
        }

        .company-contact {
            font-size: 8px;
            margin-top: 5px;
        }

        .recipient-box {
            border: 1px solid #000;
            padding: 8px;
            font-size: 10px;
            min-height: 60px;
        }

        .recipient-label {
            font-size: 9px;
            margin-bottom: 5px;
        }

        .logo-placeholder {
            border: 1px solid #000;
            width: 80px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            text-align: center;
        }

        .title {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            text-transform: uppercase;
        }

        .do-info {
            display: table;
            width: 100%;
            margin-bottom: 15px;
            font-size: 11px;
        }

        .do-number {
            display: table-cell;
            width: 50%;
            text-align: left;
        }

        .do-date {
            display: table-cell;
            width: 50%;
            text-align: right;
        }

        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 8px 4px;
            text-align: center;
            vertical-align: middle;
        }

        .main-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 9px;
        }

        .main-table td {
            min-height: 25px;
        }

        .info-section {
            display: table;
            width: 100%;
            margin-bottom: 20px;
            font-size: 10px;
        }

        .info-left,
        .info-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 0 10px;
        }

        .info-table {
            width: 100%;
        }

        .info-table td {
            padding: 2px 5px;
            border: none;
        }

        .signature-section {
            display: table;
            width: 100%;
            margin-top: 30px;
            font-size: 10px;
        }

        .signature-box {
            display: table-cell;
            width: 25%;
            text-align: center;
            vertical-align: top;
            padding: 0 5px;
        }

        .signature-title {
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .signature-space {
            height: 50px;
            border-bottom: 1px solid #000;
            margin: 10px 0;
        }

        .signature-name {
            margin-top: 5px;
        }

        .notes-section {
            margin-top: 20px;
            font-size: 10px;
        }

        .notes-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .notes-content {
            border: 1px solid #000;
            padding: 10px;
            min-height: 40px;
        }

        .page-break {
            page-break-after: always;
        }

        @media print {
            body {
                margin: 0;
            }
        }
    </style>
</head>

<body>
    <!-- Header Section -->
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <?php if(isset($logoBase64) && !empty($logoBase64)): ?>
                    <img src="data:image/png;base64,<?php echo e($logoBase64); ?>" alt="Company Logo" width="80" height="60">
                <?php else: ?>
                    <div class="logo-placeholder">
                        COMPANY<br>LOGO
                    </div>
                <?php endif; ?>
            </div>
            <div class="company-info">
                <div class="company-name">LINTAS RIAU PRIMA</div>
                <div class="company-tagline">TRUSTED & RELIABLE PARTNER</div>
                <div class="company-tagline">Fuel Agent - Fuel Transportation - Bunker Service</div>
                <div class="company-contact">
                    <?php if($record->letterSetting): ?>
                        <?php echo e($record->letterSetting->phone_number ?? '0761-22369'); ?> -
                        <?php echo e($record->letterSetting->email ?? '<EMAIL>'); ?><br>
                        <?php echo e($record->letterSetting->website ?? 'www.lintasriauprima.com'); ?>

                    <?php else: ?>
                        0761-22369 - <EMAIL><br>
                        www.lintasriauprima.com
                    <?php endif; ?>
                </div>
            </div>
            <div class="recipient-info">
                <div class="recipient-box">
                    <div class="recipient-label">To:</div>
                    <div style="font-weight: bold; font-size: 11px;">
                        <?php echo e(strtoupper($record->transaksi->pelanggan->nama ?? 'PT. ANUGERAH PRAMUDITA UTAMA')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Title -->
    <div class="title">
        GOODS DELIVERY RECEIPT / DELIVERY ORDER (DO)
    </div>

    <!-- DO Information -->
    <div class="do-info">
        <div class="do-number">
            <strong>DO Number : <?php echo e($record->kode ?? 'N/A'); ?></strong>
        </div>
        <div class="do-date">
            <strong>LRP-Form-Ops-04/Rev 03/
                <?php echo e($record->transaksi && $record->transaksi->created_at ? $record->transaksi->created_at->format('d M Y') : now()->format('d M Y')); ?></strong>
        </div>
    </div>

    <!-- Items Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th style="width: 5%;">No</th>
                <th style="width: 40%;">GOODS TYPE</th>
                <th style="width: 15%;">QUANTITY</th>
                <th style="width: 25%;">SEAL NO</th>
                <th style="width: 15%;">RECEIVER/PIC</th>
                <th style="width: 15%;">SIGNATURE</th>
            </tr>
        </thead>
        <tbody>
            <?php
                $totalVolume = 0;
                $itemNumber = 1;
            ?>

            <?php if($record->details && $record->details->count() > 0): ?>
                <?php $__currentLoopData = $record->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $totalVolume += $detail->volume_delivered ?? 0;
                    ?>
                    <tr>
                        <td><?php echo e($itemNumber++); ?></td>
                        <td style="text-align: left; padding-left: 8px;">
                            <?php echo e($detail->item_name ?? ($detail->item->name ?? 'N/A')); ?>

                        </td>
                        <td><?php echo e(number_format($detail->volume_delivered ?? 0, 0, ',', '.')); ?> L</td>
                        <td>
                            <?php if($record->seals && $record->seals->count() > 0): ?>
                                <?php echo e($record->seals->pluck('nomor_segel')->implode(', ')); ?>

                            <?php else: ?>
                                <?php echo e($record->no_segel ?? '-'); ?>

                            <?php endif; ?>
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <?php if($record->transaksi && $record->transaksi->penjualanDetails): ?>
                    <?php $__currentLoopData = $record->transaksi->penjualanDetails; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $volume = $detail->volume_item ?? 0;
                            $totalVolume += $volume;
                        ?>
                        <tr>
                            <td><?php echo e($itemNumber++); ?></td>
                            <td style="text-align: left; padding-left: 8px;">
                                <?php echo e($detail->item->name ?? 'N/A'); ?>

                            </td>
                            <td><?php echo e(number_format($volume, 0, ',', '.')); ?> L</td>
                            <td>
                                <?php if($record->seals && $record->seals->count() > 0): ?>
                                    <?php echo e($record->seals->pluck('nomor_segel')->implode(', ')); ?>

                                <?php else: ?>
                                    <?php echo e($record->no_segel ?? '-'); ?>

                                <?php endif; ?>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <tr>
                        <td>1</td>
                        <td style="text-align: left; padding-left: 8px;">
                            Fuel Product
                        </td>
                        <td><?php echo e(number_format($record->volume_do ?? 0, 0, ',', '.')); ?> L</td>
                        <td>
                            <?php if($record->seals && $record->seals->count() > 0): ?>
                                <?php echo e($record->seals->pluck('nomor_segel')->implode(', ')); ?>

                            <?php else: ?>
                                <?php echo e($record->no_segel ?? '-'); ?>

                            <?php endif; ?>
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                <?php endif; ?>
            <?php endif; ?>

            <!-- Empty rows for manual filling -->
            <?php for($i = $itemNumber; $i <= 5; $i++): ?>
                <tr>
                    <td><?php echo e($i); ?></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            <?php endfor; ?>

            <!-- Total row -->
            <tr style="font-weight: bold;">
                <td colspan="2" style="text-align: center;">TOTAL</td>
                <td><?php echo e(number_format($totalVolume, 0, ',', '.')); ?> L</td>
                <td colspan="3"></td>
            </tr>
        </tbody>
    </table>



    <!-- Notes Section -->
    <div class="notes-section">
        <div class="notes-title">Notes:</div>
        <div class="notes-content">
            • Check the quality and quantity of goods before unloading, complaints after unloading will not be
            served<br>
            • Check all top and bottom seals must be in good condition before unloading
        </div>
    </div>

    <!-- Signature Section -->
    <div style="margin-top: 30px;">
        <!-- Top row with 2 columns -->
        <div style="display: table; width: 100%; margin-bottom: 10px; font-size: 10px;">
            <div style="display: table-cell; width: 50%; text-align: left;">
                ........................./......................... 20...<br>
                <strong>Receiver,</strong><br>
                <span style="font-size: 9px;">Security (if any),</span>
            </div>
            <div style="display: table-cell; width: 50%; text-align: right;">
                Pekanbaru, /......................... 20...<br>
                <strong>Sender,</strong><br>
                <span style="font-size: 9px;">Sender,</span>
            </div>
        </div>

        <!-- Bottom row with 4 columns -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-space"></div>
                <div class="signature-name">
                    <strong>(...........................)</strong><br>
                    Name & Signature<br>
                    or Company Stamp
                </div>
            </div>
            <div class="signature-box">
                <div class="signature-space"></div>
                <div class="signature-name">
                    <strong>(...........................)</strong><br>
                    Name & Signature<br>
                    or Company Stamp
                </div>
            </div>
            <div class="signature-box">
                <div class="signature-space"></div>
                <div class="signature-name">
                    <strong>( Zaiful Amri )</strong><br>
                    BM 8524 JO<br>
                    Name & Signature
                </div>
            </div>
            <div class="signature-box">
                <div class="signature-space"></div>
                <div class="signature-name">
                    <strong>(...........................)</strong><br>
                    Name & Signature<br>
                    or Company Stamp
                </div>
            </div>
        </div>
    </div>


</body>

</html>
<?php /**PATH D:\laragon\www\lrp\resources\views/pdf/delivery_order_en.blade.php ENDPATH**/ ?>