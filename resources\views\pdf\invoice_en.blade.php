<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ $record->nomor_invoice }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            margin: 20px;
        }

        .header {
            width: 100%;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }

        .header-content {
            display: table;
            width: 100%;
        }

        .logo-section {
            display: table-cell;
            width: 25%;
            vertical-align: top;
        }

        .company-info {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            text-align: center;
            padding: 0 10px;
        }

        .invoice-info {
            display: table-cell;
            width: 25%;
            vertical-align: top;
            text-align: right;
        }

        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .company-tagline {
            font-size: 9px;
            margin-bottom: 1px;
        }

        .company-contact {
            font-size: 8px;
            margin-top: 5px;
        }

        .invoice-box {
            border: 1px solid #000;
            padding: 8px;
            font-size: 10px;
            min-height: 60px;
        }

        .invoice-label {
            font-size: 9px;
            margin-bottom: 5px;
        }

        .logo-placeholder {
            border: 1px solid #000;
            width: 80px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            text-align: center;
        }

        .title {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            text-transform: uppercase;
        }

        .invoice-details {
            display: table;
            width: 100%;
            margin-bottom: 15px;
            font-size: 10px;
        }

        .detail-left,
        .detail-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 0 10px;
        }

        .detail-table {
            width: 100%;
        }

        .detail-table td {
            padding: 2px 5px;
            border: none;
        }

        .detail-table td:first-child {
            font-weight: bold;
            width: 40%;
        }

        .detail-table td:nth-child(2) {
            width: 5%;
            text-align: center;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #000;
            padding: 8px 4px;
            text-align: center;
            vertical-align: middle;
        }

        .items-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 9px;
        }

        .items-table td {
            min-height: 25px;
        }

        .totals-section {
            float: right;
            width: 300px;
            margin-bottom: 20px;
        }

        .totals-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
        }

        .totals-table td {
            border: 1px solid #000;
            padding: 5px 8px;
        }

        .totals-table td:first-child {
            font-weight: bold;
            background-color: #f0f0f0;
            text-align: right;
        }

        .totals-table td:last-child {
            text-align: right;
        }

        .total-final {
            background-color: #000 !important;
            color: white !important;
            font-weight: bold !important;
        }

        .notes-section {
            clear: both;
            margin-top: 20px;
            font-size: 10px;
        }

        .notes-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .notes-content {
            border: 1px solid #000;
            padding: 10px;
            min-height: 40px;
        }

        .signature-section {
            display: table;
            width: 100%;
            margin-top: 30px;
            font-size: 10px;
        }

        .signature-box {
            display: table-cell;
            width: 50%;
            text-align: center;
            vertical-align: top;
            padding: 0 10px;
        }

        .signature-title {
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .signature-space {
            height: 60px;
            border-bottom: 1px solid #000;
            margin: 10px 0;
        }

        .signature-name {
            margin-top: 5px;
        }

        .page-break {
            page-break-after: always;
        }

        @media print {
            body {
                margin: 0;
            }
        }
    </style>
</head>

<body>
    <!-- Header Section -->
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                @if (isset($logoBase64) && !empty($logoBase64))
                    <img src="data:image/png;base64,{{ $logoBase64 }}" alt="Company Logo" width="80" height="60">
                @else
                    <div class="logo-placeholder">
                        COMPANY<br>LOGO
                    </div>
                @endif
            </div>
            <div class="company-info">
                <div class="company-name">LINTAS RIAU PRIMA</div>
                <div class="company-tagline">TRUSTED & RELIABLE PARTNER</div>
                <div class="company-tagline">Fuel Agent - Fuel Transportation - Bunker Service</div>
                <div class="company-contact">
                    @if ($record->letterSetting)
                        {{ $record->letterSetting->phone_number ?? '0761-22369' }} -
                        {{ $record->letterSetting->email ?? '<EMAIL>' }}<br>
                        {{ $record->letterSetting->website ?? 'www.lintasriauprima.com' }}
                    @else
                        0761-22369 - <EMAIL><br>
                        www.lintasriauprima.com
                    @endif
                </div>
            </div>
            <div class="invoice-info">
                <div class="invoice-box">
                    <div class="invoice-label">Invoice No:</div>
                    <div style="font-weight: bold; font-size: 11px;">
                        {{ $record->nomor_invoice ?? 'N/A' }}
                    </div>
                    <div style="margin-top: 5px;">
                        <div class="invoice-label">Status:</div>
                        <div style="font-weight: bold;">
                            {{ ucfirst($record->status ?? 'draft') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Title -->
    <div class="title">
        SALES INVOICE
    </div>

    <!-- Invoice Details -->
    <div class="invoice-details">
        <div class="detail-left">
            <h4 style="font-size: 11px; margin-bottom: 10px; border-bottom: 1px solid #000; padding-bottom: 3px;">
                Customer Information</h4>
            <table class="detail-table">
                <tr>
                    <td>Customer</td>
                    <td>:</td>
                    <td>{{ $record->nama_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->nama ?? 'N/A') }}</td>
                </tr>
                <tr>
                    <td>Address</td>
                    <td>:</td>
                    <td>{{ $record->alamat_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->alamat ?? 'N/A') }}
                    </td>
                </tr>
                <tr>
                    <td>Tax ID</td>
                    <td>:</td>
                    <td>{{ $record->npwp_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->npwp ?? '-') }}</td>
                </tr>
            </table>
        </div>
        <div class="detail-right">
            <h4 style="font-size: 11px; margin-bottom: 10px; border-bottom: 1px solid #000; padding-bottom: 3px;">
                Invoice Information</h4>
            <table class="detail-table">
                <tr>
                    <td>Invoice Date</td>
                    <td>:</td>
                    <td>{{ $record->tanggal_invoice ? $record->tanggal_invoice->format('d M Y') : 'N/A' }}</td>
                </tr>
                <tr>
                    <td>Due Date</td>
                    <td>:</td>
                    <td>{{ $record->tanggal_jatuh_tempo ? $record->tanggal_jatuh_tempo->format('d M Y') : 'N/A' }}</td>
                </tr>
                <tr>
                    <td>Transaction</td>
                    <td>:</td>
                    <td>{{ $record->transaksiPenjualan?->kode ?? 'N/A' }}</td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 5%;">No</th>
                <th style="width: 45%;">Description</th>
                <th style="width: 15%;">Quantity</th>
                <th style="width: 15%;">Unit Price</th>
                <th style="width: 20%;">Total</th>
            </tr>
        </thead>
        <tbody>
            @php
                $itemNumber = 1;
                $subtotalCalculated = 0;
            @endphp

            @if ($record->invoiceItems && $record->invoiceItems->count() > 0)
                @foreach ($record->invoiceItems as $item)
                    @php
                        $total = $item->quantity * $item->unit_price;
                        $subtotalCalculated += $total;
                    @endphp
                    <tr>
                        <td>{{ $itemNumber++ }}</td>
                        <td style="text-align: left; padding-left: 8px;">
                            {{ $item->description ?? ($item->item?->name ?? 'N/A') }}
                        </td>
                        <td>{{ number_format($item->quantity ?? 0, 0, ',', '.') }} {{ $item->unit ?? 'L' }}</td>
                        <td>{{ $record->formatCurrency($item->unit_price ?? 0) }}</td>
                        <td>{{ $record->formatCurrency($total) }}</td>
                    </tr>
                @endforeach
            @else
                @if ($record->transaksiPenjualan && $record->transaksiPenjualan->penjualanDetails)
                    @foreach ($record->transaksiPenjualan->penjualanDetails as $detail)
                        @php
                            $total = $detail->volume_item * $detail->harga_jual;
                            $subtotalCalculated += $total;
                        @endphp
                        <tr>
                            <td>{{ $itemNumber++ }}</td>
                            <td style="text-align: left; padding-left: 8px;">
                                {{ $detail->item->name ?? 'N/A' }}
                            </td>
                            <td>{{ number_format($detail->volume_item ?? 0, 0, ',', '.') }} L</td>
                            <td>{{ $record->formatCurrency($detail->harga_jual ?? 0) }}</td>
                            <td>{{ $record->formatCurrency($total) }}</td>
                        </tr>
                    @endforeach
                @endif
            @endif

            <!-- Empty rows for manual filling -->
            @for ($i = $itemNumber; $i <= 5; $i++)
                <tr>
                    <td>{{ $i }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            @endfor
        </tbody>
    </table>

    <!-- Totals Section -->
    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td>Subtotal</td>
                <td>{{ $record->formatCurrency($record->subtotal ?? $subtotalCalculated) }}</td>
            </tr>
            @if ($record->include_ppn)
                <tr>
                    <td>VAT 11%</td>
                    <td>{{ $record->formatCurrency($record->total_pajak ?? 0) }}</td>
                </tr>
            @endif
            @if ($record->include_operasional_kerja && $record->biaya_operasional_kerja > 0)
                <tr>
                    <td>Operational Fee</td>
                    <td>{{ $record->formatCurrency($record->biaya_operasional_kerja ?? 0) }}</td>
                </tr>
            @endif
            @if ($record->include_pbbkb && $record->biaya_pbbkb > 0)
                <tr>
                    <td>PBBKB Fee</td>
                    <td>{{ $record->formatCurrency($record->biaya_pbbkb ?? 0) }}</td>
                </tr>
            @endif
            <tr class="total-final">
                <td>Total Invoice</td>
                <td>{{ $record->formatCurrency($record->total_invoice ?? ($record->calculated_total ?? 0)) }}</td>
            </tr>
        </table>
    </div>

    <!-- Notes Section -->
    <div class="notes-section">
        <div class="notes-title">Notes:</div>
        <div class="notes-content">
            {{ $record->catatan ?? '' }}
        </div>
    </div>

    <!-- Signature Section -->
    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-title">Issuer</div>
            <div class="signature-space"></div>
            <div class="signature-name">
                <strong>PT. LINTAS RIAU PRIMA</strong><br>
                Authorized Signature
            </div>
        </div>
        <div class="signature-box">
            <div class="signature-title">Recipient</div>
            <div class="signature-space"></div>
            <div class="signature-name">
                <strong>{{ $record->nama_pelanggan ?? 'Customer Name' }}</strong><br>
                Name & Signature
            </div>
        </div>
    </div>

    <!-- Footer with company address -->
    <div style="margin-top: 30px; text-align: center; font-size: 9px; border-top: 1px solid #000; padding-top: 10px;">
        <strong>PT. LINTAS RIAU PRIMA</strong><br>
        @if ($record->letterSetting)
            {{ $record->letterSetting->address }}<br>
            Phone: {{ $record->letterSetting->phone_number }} | Email: {{ $record->letterSetting->email }} | Website:
            {{ $record->letterSetting->website }}
        @else
            Jl. Raya Lintas Timur KM 16, Kelurahan Tuah Karya, Kecamatan Tampan, Kota Pekanbaru, Riau 28293<br>
            Phone: 0761-22369 | Email: <EMAIL> | Website: www.lintasriauprima.com
        @endif
    </div>
</body>

</html>
