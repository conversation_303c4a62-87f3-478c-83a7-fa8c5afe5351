<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class PengirimanDriver extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    protected $table = 'pengiriman_driver';

    protected $fillable = [
        'id_do',
        'totalisator_awal',
        'totalisator_tiba',
        'totalisator_pool_return',
        'waktu_mulai',
        'waktu_tiba',
        'waktu_pool_arrival',
        'approval_status',
        'approved_by',
        'approved_at',
        'approval_notes',
        'created_by',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'waktu_mulai' => 'datetime',
        'waktu_tiba' => 'datetime',
        'waktu_pool_arrival' => 'datetime',
        'approved_at' => 'datetime',
        'totalisator_awal' => 'float',
        'totalisator_tiba' => 'float',
        'totalisator_pool_return' => 'float',
    ];

    public function deliveryOrder(): BelongsTo
    {
        return $this->belongsTo(DeliveryOrder::class, 'id_do');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function deliveryEvidences(): HasMany
    {
        return $this->hasMany(DeliveryEvidence::class, 'id_pengiriman_driver');
    }

    // Approval workflow methods
    public function canBeApproved(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function canBeRejected(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    public function isRejected(): bool
    {
        return $this->approval_status === 'rejected';
    }

    public function isPending(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function approve(User $approver, ?string $notes = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        return $this->update([
            'approval_status' => 'approved',
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);
    }

    public function reject(User $approver, ?string $notes = null): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        return $this->update([
            'approval_status' => 'rejected',
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('foto_pengiriman')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('foto_totalizer_awal')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('foto_totalizer_akhir')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        // Bukti-bukti pengiriman
        $this->addMediaCollection('foto_pemutusan_segel_atas')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('foto_pemutusan_segel_bawah')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('foto_tera')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('foto_sample_bbm')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('foto_tangki_mt_kosong')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('foto_mt')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('foto_pembongkaran')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
    }
    public function registerMediaConversions(?Media $media = null): void
    {
        $collections = [
            'foto_pengiriman',
            'foto_totalizer_awal',
            'foto_totalizer_akhir',
            'foto_pemutusan_segel_atas',
            'foto_pemutusan_segel_bawah',
            'foto_tera',
            'foto_sample_bbm',
            'foto_tangki_mt_kosong',
            'foto_mt',
            'foto_pembongkaran'
        ];

        // Thumbnail conversion for grid display (compressed)
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->quality(80)
            ->sharpen(10)
            ->performOnCollections(...$collections)
            ->nonQueued();

        // Preview conversion for modal display (compressed)
        $this->addMediaConversion('preview')
            ->width(400)
            ->height(400)
            ->quality(85)
            ->performOnCollections(...$collections)
            ->nonQueued();

        // Large conversion for full-size viewing (optimized)
        $this->addMediaConversion('large')
            ->width(800)
            ->height(800)
            ->quality(90)
            ->performOnCollections(...$collections)
            ->nonQueued();
    }
    public function getFotoPengirimanUrlAttribute()
    {
        return $this->getFirstMediaUrl('foto_pengiriman');
    }

    public function getFotoTotalizerAwalUrlAttribute()
    {
        return $this->getFirstMediaUrl('foto_totalizer_awal');
    }

    public function getFotoTotalizerAkhirUrlAttribute()
    {
        return $this->getFirstMediaUrl('foto_totalizer_akhir');
    }
}
