<?php

namespace App\Filament\Resources\TransaksiPenjualanResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DeliveryOrdersRelationManager extends RelationManager
{
    protected static string $relationship = 'deliveryOrders';

    protected static ?string $title = 'Delivery Orders';

    protected static ?string $modelLabel = 'Delivery Order';

    protected static ?string $pluralModelLabel = 'Delivery Orders';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('kode')
                            ->label('Nomor DO')
                            ->disabled()
                            ->helperText('Nomor DO akan di-generate otomatis'),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'pending' => 'Pending',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                                'cancelled' => 'Cancelled',
                            ])
                            ->default('pending')
                            ->required(),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\DateTimePicker::make('tanggal_delivery')
                            ->label('Tanggal Pengiriman')
                            ->default(now())
                            ->required(),

                        Forms\Components\TextInput::make('volume_do')
                            ->label('Volume DO')
                            ->numeric()
                            ->suffix('L')
                            ->helperText('Volume yang akan dikirim'),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('id_user')
                            ->label('Supir')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->placeholder('Pilih Supir'),

                        Forms\Components\Select::make('id_kendaraan')
                            ->label('Kendaraan')
                            ->relationship('kendaraan', 'no_pol_kendaraan')
                            ->searchable()
                            ->preload()
                            ->placeholder('Pilih Kendaraan'),
                    ]),

                Forms\Components\Textarea::make('catatan')
                    ->label('Catatan')
                    ->rows(3)
                    ->placeholder('Catatan tambahan untuk delivery order ini'),

                Forms\Components\Repeater::make('details')
                    ->label('Detail Item Pengiriman')
                    ->relationship('details')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('id_penjualan_detail')
                                    ->label('Item dari Transaksi')
                                    ->options(function (callable $get) {
                                        $transaksiId = $get('../../id_transaksi') ?? $this->ownerRecord->id;
                                        return \App\Models\PenjualanDetail::where('id_transaksi_penjualan', $transaksiId)
                                            ->with('item')
                                            ->get()
                                            ->mapWithKeys(function ($detail) {
                                                return [$detail->id => $detail->item->name . ' - ' . number_format($detail->volume_item, 0, ',', '.') . ' L'];
                                            });
                                    })
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                        if ($state) {
                                            $penjualanDetail = \App\Models\PenjualanDetail::with('item.satuan')->find($state);
                                            if ($penjualanDetail) {
                                                $set('id_item', $penjualanDetail->id_item);
                                                $set('item_name', $penjualanDetail->item->name);
                                                $set('item_description', $penjualanDetail->item->description);
                                                $set('volume_ordered', $penjualanDetail->volume_item);
                                                $set('unit', $penjualanDetail->item->satuan->nama ?? 'Liter');
                                                $set('unit_price', $penjualanDetail->harga_jual);

                                                // Set default volume_delivered to volume_ordered
                                                $set('volume_delivered', $penjualanDetail->volume_item);

                                                // Calculate total amount
                                                $totalAmount = $penjualanDetail->volume_item * $penjualanDetail->harga_jual;
                                                $set('total_amount', $totalAmount);
                                            }
                                        }
                                    })
                                    ->required(),

                                Forms\Components\TextInput::make('volume_ordered')
                                    ->label('Volume Dipesan')
                                    ->numeric()
                                    ->suffix('L')
                                    ->disabled(),

                                Forms\Components\TextInput::make('volume_delivered')
                                    ->label('Volume Dikirim')
                                    ->numeric()
                                    ->suffix('L')
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                        $volume = (float) $get('volume_delivered');
                                        $price = (float) $get('unit_price');
                                        $set('total_amount', $volume * $price);
                                    }),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('unit_price')
                                    ->label('Harga Satuan')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled(),

                                Forms\Components\TextInput::make('total_amount')
                                    ->label('Total')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled(),
                            ]),

                        Forms\Components\Hidden::make('id_item'),
                        Forms\Components\Hidden::make('item_name'),
                        Forms\Components\Hidden::make('item_description'),
                        Forms\Components\Hidden::make('unit'),
                    ])
                    ->defaultItems(1)
                    ->addActionLabel('Tambah Item')
                    ->collapsible()
                    ->cloneable(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('kode')
            ->description(function () {
                $totalDOs = $this->ownerRecord->deliveryOrders()->count();
                $totalVolumeDelivered = $this->ownerRecord->deliveryOrders()
                    ->join('delivery_order_details', 'delivery_order.id', '=', 'delivery_order_details.id_delivery_order')
                    ->sum('delivery_order_details.volume_delivered') ?? 0;
                $transaksiVolume = $this->ownerRecord->penjualanDetails()->sum('volume_item') ?? 0;
                $pendingDOs = $this->ownerRecord->deliveryOrders()->where('status', 'pending')->count();
                $completedDOs = $this->ownerRecord->deliveryOrders()->where('status', 'completed')->count();
                $totalItems = $this->ownerRecord->penjualanDetails()->count();

                $percentage = $transaksiVolume > 0 ? round(($totalVolumeDelivered / $transaksiVolume) * 100, 1) : 0;

                return "📦 {$totalDOs} DO | ⏳ {$pendingDOs} Pending | ✅ {$completedDOs} Selesai | � {$totalItems} Items | 🚛 " . number_format($totalVolumeDelivered, 0, ',', '.') . "L / " . number_format($transaksiVolume, 0, ',', '.') . "L ({$percentage}%)";
            })
            ->columns([
                Tables\Columns\TextColumn::make('kode')
                    ->label('Nomor DO')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_delivery')
                    ->label('Tanggal Pengiriman')
                    ->dateTime('d M Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Supir')
                    ->searchable()
                    ->placeholder('Belum Ditugaskan'),

                Tables\Columns\TextColumn::make('kendaraan.no_pol_kendaraan')
                    ->label('Kendaraan')
                    ->searchable()
                    ->placeholder('Belum Ditugaskan'),

                Tables\Columns\TextColumn::make('volume_do')
                    ->label('Total Volume')
                    ->numeric()
                    ->suffix(' L')
                    ->sortable()
                    ->description(function ($record) {
                        $itemCount = $record->details()->count();
                        return $itemCount > 0 ? "{$itemCount} item" : 'Belum ada detail';
                    }),

                Tables\Columns\TextColumn::make('details_summary')
                    ->label('Detail Items')
                    ->html()
                    ->getStateUsing(function ($record) {
                        $details = $record->details()->with('item')->get();
                        if ($details->isEmpty()) {
                            return '<span class="text-gray-500">Belum ada detail item</span>';
                        }

                        $summary = $details->map(function ($detail) {
                            return $detail->item->name . ': ' . number_format($detail->volume_delivered, 0, ',', '.') . 'L';
                        })->take(2)->implode('<br>');

                        if ($details->count() > 2) {
                            $summary .= '<br><span class="text-gray-500">+' . ($details->count() - 2) . ' item lainnya</span>';
                        }

                        return $summary;
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'in_progress' => 'info',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Buat Delivery Order')
                    ->icon('heroicon-o-truck')
                    ->color('success')
                    ->modalHeading('Buat Delivery Order Baru')
                    ->modalDescription('Buat delivery order untuk transaksi ' . $this->ownerRecord->nomor_transaksi)
                    ->modalWidth('2xl')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['id_transaksi'] = $this->ownerRecord->id;
                        $data['created_by'] = \Illuminate\Support\Facades\Auth::id() ?? 1;

                        // Set default tanggal if not provided
                        if (empty($data['tanggal_delivery'])) {
                            $data['tanggal_delivery'] = now();
                        }

                        // Set default status if not provided
                        if (empty($data['status'])) {
                            $data['status'] = 'pending';
                        }

                        // Calculate total volume from details
                        if (isset($data['details']) && is_array($data['details'])) {
                            $totalVolume = collect($data['details'])->sum('volume_delivered');
                            $data['volume_do'] = $totalVolume;
                        }

                        return $data;
                    })
                    ->successNotificationTitle('Delivery Order berhasil dibuat')
                    ->createAnother(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->color('info'),

                Tables\Actions\EditAction::make()
                    ->color('warning'),

                Tables\Actions\Action::make('duplicate')
                    ->label('Duplikat')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('secondary')
                    ->action(function ($record) {
                        $newDO = $record->replicate();
                        $newDO->kode = null; // Will be auto-generated
                        $newDO->status = 'pending';
                        $newDO->created_at = now();
                        $newDO->updated_at = now();
                        $newDO->save();

                        \Filament\Notifications\Notification::make()
                            ->title('Delivery Order berhasil diduplikat')
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Duplikat Delivery Order')
                    ->modalDescription('Apakah Anda yakin ingin menduplikat delivery order ini?')
                    ->tooltip('Duplikat delivery order ini'),

                Tables\Actions\DeleteAction::make()
                    ->color('danger'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
