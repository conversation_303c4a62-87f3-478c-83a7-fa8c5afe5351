<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\NotificationEvent;
use App\Models\NotificationSetting;

class TransaksiPenjualanNotificationSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get notification events
        $penjualanApprovedEvent = NotificationEvent::where('event_name', 'penjualan_approved')->first();
        $penjualanBelumSelesaiEvent = NotificationEvent::where('event_name', 'penjualan_belum_selesai')->first();

        if (!$penjualanApprovedEvent || !$penjualanBelumSelesaiEvent) {
            $this->command->error('Notification events not found. Please run the migration first.');
            return;
        }

        // Get users with specific roles for default notification settings
        $operationalManagers = User::where('role', 'manager_operasional')
            ->orWhere('role', 'admin')
            ->orWhere('role', 'superadmin')
            ->get();

        $managers = User::where('role', 'manager')
            ->orWhere('role', 'admin')
            ->orWhere('role', 'superadmin')
            ->get();

        // Create notification settings for penjualan_approved (to operational managers)
        foreach ($operationalManagers as $user) {
            NotificationSetting::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'event_name' => 'penjualan_approved',
                ],
                [
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }

        // Create notification settings for penjualan_belum_selesai (to managers and admins)
        foreach ($managers as $user) {
            NotificationSetting::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'event_name' => 'penjualan_belum_selesai',
                ],
                [
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }

        $this->command->info('Transaksi Penjualan notification settings seeded successfully.');
    }
}
