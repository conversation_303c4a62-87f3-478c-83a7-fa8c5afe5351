<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PaymentMethod;
use Illuminate\Support\Facades\DB;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * This will populate the payment_methods table with default values.
     */
    public function run(): void
    {
        // Use DB::statement to disable foreign key checks temporarily, which can be safer for seeding.
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        PaymentMethod::truncate(); // Optional: Clears the table before seeding
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $paymentMethods = [
            // Bank Transfer Methods
            [
                'method_name' => 'bank_transfer',
                'method_display_name' => 'BNI Kantor Pusat',
                'bank_name' => 'Bank BNI',
                'account_number' => str_pad(mt_rand(1, **********), 10, '0', STR_PAD_LEFT),
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Rekening utama untuk transaksi besar',
                'is_active' => true,
            ],
            [
                'method_name' => 'bank_transfer',
                'method_display_name' => 'Mandiri Operasional',
                'bank_name' => 'Bank Mandiri',
                'account_number' => str_pad(mt_rand(1, **********999), 13, '0', STR_PAD_LEFT),
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Rekening untuk operasional harian',
                'is_active' => true,
            ],
            [
                'method_name' => 'bank_transfer',
                'method_display_name' => 'BCA Payroll',
                'bank_name' => 'Bank Central Asia (BCA)',
                'account_number' => str_pad(mt_rand(1, **********), 10, '0', STR_PAD_LEFT),
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Khusus untuk payroll karyawan',
                'is_active' => true,
            ],
            [
                'method_name' => 'bank_transfer',
                'method_display_name' => 'BRI Cabang Pekanbaru',
                'bank_name' => 'Bank Rakyat Indonesia (BRI)',
                'account_number' => str_pad(mt_rand(1, **********99999), 15, '0', STR_PAD_LEFT),
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Rekening regional Pekanbaru',
                'is_active' => true,
            ],

            // Cash Methods
            [
                'method_name' => 'cash',
                'method_display_name' => 'Kas Kecil Kantor',
                'bank_name' => null,
                'account_number' => null,
                'account_name' => 'Kasir Kantor',
                'notes' => 'Untuk pembayaran tunai operasional kantor',
                'is_active' => true,
            ],
            [
                'method_name' => 'cash',
                'method_display_name' => 'Kas Lapangan',
                'bank_name' => null,
                'account_number' => null,
                'account_name' => 'Supervisor Lapangan',
                'notes' => 'Untuk pembayaran tunai di lapangan',
                'is_active' => true,
            ],

            // Other Payment Methods
            [
                'method_name' => 'check',
                'method_display_name' => 'Cek Bank BNI',
                'bank_name' => 'Bank BNI',
                'account_number' => null,
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Untuk pembayaran dengan cek',
                'is_active' => true,
            ],
            [
                'method_name' => 'giro',
                'method_display_name' => 'Giro Bank Mandiri',
                'bank_name' => 'Bank Mandiri',
                'account_number' => null,
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Untuk pembayaran dengan giro',
                'is_active' => true,
            ],
        ];

        // Loop through the data and create records
        foreach ($paymentMethods as $method) {
            // Find appropriate COA based on method type
            $akunId = $this->findCOAForPaymentMethod($method);
            if ($akunId) {
                $method['akun_id'] = $akunId;
            }

            // Use updateOrCreate with method_display_name as unique key to prevent duplicates
            PaymentMethod::updateOrCreate(
                ['method_display_name' => $method['method_display_name']], // The unique key to check
                $method  // The data to insert or update
            );
        }
    }

    /**
     * Find appropriate COA for payment method
     */
    private function findCOAForPaymentMethod(array $method): ?int
    {
        $akunId = null;

        switch ($method['method_name']) {
            case 'cash':
                if (str_contains(strtolower($method['method_display_name']), 'kecil')) {
                    $akunId = \App\Models\Akun::where('kode_akun', '1111')->value('id'); // Kas Kecil
                } else {
                    $akunId = \App\Models\Akun::where('kode_akun', '1110')->value('id'); // Kas di Tangan
                }
                break;

            case 'bank_transfer':
                $bankName = strtolower($method['bank_name'] ?? '');
                if (str_contains($bankName, 'bni')) {
                    $akunId = \App\Models\Akun::where('kode_akun', '1120')->value('id');
                } elseif (str_contains($bankName, 'mandiri')) {
                    $akunId = \App\Models\Akun::where('kode_akun', '1121')->value('id');
                } elseif (str_contains($bankName, 'bca')) {
                    $akunId = \App\Models\Akun::where('kode_akun', '1122')->value('id');
                } elseif (str_contains($bankName, 'bri')) {
                    $akunId = \App\Models\Akun::where('kode_akun', '1123')->value('id');
                }
                break;

            case 'check':
                $akunId = \App\Models\Akun::where('kode_akun', '1130')->value('id');
                break;

            case 'giro':
                $akunId = \App\Models\Akun::where('kode_akun', '1131')->value('id');
                break;

            case 'e_wallet':
                $akunId = \App\Models\Akun::where('kode_akun', '1140')->value('id');
                break;
        }

        return $akunId;
    }
}
