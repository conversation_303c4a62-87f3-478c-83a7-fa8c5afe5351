<?php

namespace App\Filament\Resources\InvoiceResource\Pages;

use App\Filament\Resources\InvoiceResource;
use Filament\Resources\Pages\CreateRecord;

class CreateInvoice extends CreateRecord
{
    protected static string $resource = InvoiceResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Auto-populate from URL parameter when creating new invoice
        if (request()->has('id_transaksi')) {
            $idTransaksi = request()->get('id_transaksi');
            $transaksi = \App\Models\TransaksiPenjualan::with('pelanggan', 'alamatPelanggan', 'penjualanDetails.item')->find($idTransaksi);

            if ($transaksi) {
                $data['id_transaksi'] = $transaksi->id;

                // Auto-populate customer data
                if ($transaksi->pelanggan) {
                    $data['nama_pelanggan'] = $transaksi->pelanggan->nama;
                    $data['npwp_pelanggan'] = $transaksi->pelanggan->npwp;
                }

                // Auto-populate address
                if ($transaksi->alamatPelanggan) {
                    $alamatLengkap = collect([
                        $transaksi->alamatPelanggan->alamat,
                        $transaksi->alamatPelanggan->kelurahan,
                        $transaksi->alamatPelanggan->kecamatan,
                        $transaksi->alamatPelanggan->kota,
                        $transaksi->alamatPelanggan->provinsi,
                        $transaksi->alamatPelanggan->kode_pos
                    ])->filter()->implode(', ');

                    $data['alamat_pelanggan'] = $alamatLengkap;
                } else {
                    $data['alamat_pelanggan'] = $transaksi->pelanggan->alamat ?? '';
                }

                // Calculate subtotal from penjualan details
                $subtotal = 0;
                $totalVolume = 0;

                foreach ($transaksi->penjualanDetails as $detail) {
                    $itemTotal = ($detail->volume_do ?? $detail->volume_item) * $detail->harga_jual;
                    $subtotal += $itemTotal;
                    $totalVolume += ($detail->volume_do ?? $detail->volume_item);
                }

                // Set calculated values
                $data['subtotal'] = $subtotal;
                $data['operasional_volume'] = $totalVolume;
                $data['pbbkb_volume'] = $totalVolume;

                // Set default dates
                $data['tanggal_invoice'] = now();
                $data['tanggal_jatuh_tempo'] = now()->addDays(30);

                // Set default status
                $data['status'] = 'draft';
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Remove id_do if present (no longer needed)
        unset($data['id_do']);

        // Set created_by
        $data['created_by'] = \Illuminate\Support\Facades\Auth::id();

        return $data;
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCreateAnotherFormAction(),
            $this->getCancelFormAction(),
        ];
    }

    protected function afterCreate(): void
    {
        // Auto posting journal entry when invoice is created
        $this->record->createJournalEntry();
    }
}
