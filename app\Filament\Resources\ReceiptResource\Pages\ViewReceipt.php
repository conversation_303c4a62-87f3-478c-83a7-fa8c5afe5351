<?php

namespace App\Filament\Resources\ReceiptResource\Pages;

use App\Filament\Resources\ReceiptResource;
use App\Models\Receipt;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\View;

class ViewReceipt extends ViewRecord
{
    protected static string $resource = ReceiptResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('Preview Kwitansi')
                ->color('gray')
                ->icon('heroicon-o-eye')
                ->action(null)
                ->modalContent(function (Receipt $record): \Illuminate\View\View {
                    // Load the record with all necessary relationships
                    $record->load([
                        'invoice.transaksiPenjualan.pelanggan.alamatUtama',
                        'transaksiPenjualan.pelanggan.alamatUtama',
                        'deliveryOrder',
                        'createdBy.jabatan'
                    ]);

                    return View::make('receipt.receipt-preview', ['record' => $record]);
                })
                ->modalHeading("Preview: {$this->record->nomor_receipt}")
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Tutup')
                ->slideOver()
                ->modalWidth('4xl'),

            Actions\EditAction::make(),
        ];
    }
}
