<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceItem extends Model
{
    protected $table = 'invoice_items';

    protected $fillable = [
        'invoice_id',
        'item_id',
        'item_name',
        'item_description',
        'quantity',
        'unit',
        'unit_price',
        'subtotal',
        'ppn_amount',
        'total_amount',
        'include_ppn',
        'ppn_rate',
        'include_operasional',
        'operasional_rate',
        'operasional_amount',
        'include_pbbkb',
        'pbbkb_rate',
        'pbbkb_amount',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'ppn_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'ppn_rate' => 'decimal:2',
        'include_ppn' => 'boolean',
        'include_operasional' => 'boolean',
        'operasional_rate' => 'decimal:2',
        'operasional_amount' => 'decimal:2',
        'include_pbbkb' => 'boolean',
        'pbbkb_rate' => 'decimal:2',
        'pbbkb_amount' => 'decimal:2',
    ];

    /**
     * Boot method to auto-calculate amounts
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($invoiceItem) {
            // Calculate subtotal
            $invoiceItem->subtotal = $invoiceItem->quantity * $invoiceItem->unit_price;

            // Calculate PPN amount
            if ($invoiceItem->include_ppn) {
                $invoiceItem->ppn_amount = $invoiceItem->subtotal * ($invoiceItem->ppn_rate / 100);
            } else {
                $invoiceItem->ppn_amount = 0;
            }

            // Calculate operational amount (per liter)
            if ($invoiceItem->include_operasional) {
                $invoiceItem->operasional_amount = $invoiceItem->quantity * $invoiceItem->operasional_rate;
            } else {
                $invoiceItem->operasional_amount = 0;
            }

            // Calculate PBBKB amount (per liter)
            if ($invoiceItem->include_pbbkb) {
                $invoiceItem->pbbkb_amount = $invoiceItem->quantity * $invoiceItem->pbbkb_rate;
            } else {
                $invoiceItem->pbbkb_amount = 0;
            }

            // Calculate total amount (subtotal + ppn + operasional + pbbkb)
            $invoiceItem->total_amount = $invoiceItem->subtotal +
                $invoiceItem->ppn_amount +
                $invoiceItem->operasional_amount +
                $invoiceItem->pbbkb_amount;
        });
    }

    /**
     * Get the invoice that owns this item.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    /**
     * Get the item associated with this invoice item.
     */
    public function item(): BelongsTo
    {
        return $this->belongsTo(Item::class, 'item_id');
    }
}
