<?php

namespace App\Filament\Resources\TransaksiPenjualanResource\Pages;

use App\Filament\Resources\TransaksiPenjualanResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;

class CreateTransaksiPenjualan extends CreateRecord
{
    protected static string $resource = TransaksiPenjualanResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set created_by for the main transaction
        $data['created_by'] = Auth::id();

        // Handle SPH data berdasarkan input type
        if (isset($data['sph_input_type'])) {
            if ($data['sph_input_type'] === 'select' && isset($data['sph_id'])) {
                // Jika pilih dari SPH yang ada, pastikan nomor_sph terisi dari SPH
                $sph = \App\Models\Sph::find($data['sph_id']);
                if ($sph) {
                    $data['nomor_sph'] = $sph->sph_number;
                }
            } elseif ($data['sph_input_type'] === 'manual') {
                // Jika input manual, clear sph_id
                $data['sph_id'] = null;
            }

            // Remove sph_input_type karena tidak disimpan di database
            unset($data['sph_input_type']);
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        // Show success notification with item count
        $record = $this->getRecord();
        $itemCount = $record->penjualanDetails()->count();

        Notification::make()
            ->title('Berhasil')
            ->body("Transaksi penjualan berhasil dibuat dengan {$itemCount} item.")
            ->success()
            ->send();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }
}
