<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use App\Services\JournalingService;
use App\Models\Journal;

class Invoice extends Model
{
    use SoftDeletes;

    protected $table = 'invoice';

    protected $fillable = [
        // Core invoice fields (matching cleaned database schema)
        'nomor_invoice',
        'id_transaksi',
        'letter_setting_id',
        'tanggal_invoice',
        'tanggal_jatuh_tempo',
        'nama_pelanggan',
        'alamat_pelanggan',
        'npwp_pelanggan',
        'subtotal',
        'total_pajak',
        'total_invoice',
        'total_terbayar',
        'sisa_tagihan',
        'status',
        'catatan',

        // Additional cost fields
        'biaya_ongkos_angkut',
        'biaya_pbbkb',
        'biaya_operasional_kerja',
        'operasional_volume',
        'pbbkb_volume',
        'include_ppn',
        'include_pbbkb',
        'include_operasional_kerja',

        // System fields
        'journal_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'tanggal_invoice' => 'datetime',
        'tanggal_jatuh_tempo' => 'datetime',

        // Financial fields
        'subtotal' => 'decimal:2',
        'total_pajak' => 'decimal:2',
        'total_invoice' => 'decimal:2',
        'total_terbayar' => 'decimal:2',
        'sisa_tagihan' => 'decimal:2',
        'biaya_ongkos_angkut' => 'decimal:2',
        'biaya_pbbkb' => 'decimal:2',
        'biaya_operasional_kerja' => 'decimal:2',
        'operasional_volume' => 'decimal:2',
        'pbbkb_volume' => 'decimal:2',

        // Boolean flags
        'include_ppn' => 'boolean',
        'include_pbbkb' => 'boolean',
        'include_operasional_kerja' => 'boolean',
    ];

    /**
     * Get the sales transaction that owns the invoice.
     */
    public function transaksiPenjualan()
    {
        return $this->belongsTo(TransaksiPenjualan::class, 'id_transaksi');
    }

    /**
     * Get the delivery orders through the sales transaction.
     */
    public function deliveryOrders()
    {
        return $this->hasManyThrough(
            DeliveryOrder::class,
            TransaksiPenjualan::class,
            'id', // Foreign key on transaksi_penjualan table
            'id_transaksi', // Foreign key on delivery_orders table
            'id_transaksi', // Local key on invoices table
            'id' // Local key on transaksi_penjualan table
        );
    }

    /**
     * Get the receipts for the invoice.
     */
    public function receipts()
    {
        return $this->hasMany(Receipt::class, 'id_invoice');
    }

    /**
     * Get the tax invoice for the invoice.
     */
    public function taxInvoice()
    {
        return $this->hasOne(TaxInvoice::class, 'id_invoice');
    }

    /**
     * Get the user who created the invoice.
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the journal for the invoice.
     */
    public function journal()
    {
        return $this->belongsTo(Journal::class, 'journal_id');
    }

    /**
     * Get the invoice items for this invoice.
     */
    public function invoiceItems()
    {
        return $this->hasMany(InvoiceItem::class, 'invoice_id');
    }

    /**
     * Get the letter setting associated with this invoice.
     */
    public function letterSetting()
    {
        return $this->belongsTo(LetterSetting::class);
    }

    /**
     * Get the locale for this invoice based on letter setting priority.
     */
    public function getLocale()
    {
        // Priority 1: Direct letter setting
        if ($this->letterSetting) {
            return $this->letterSetting->locale;
        }

        // Priority 2: From SPH through TransaksiPenjualan
        if ($this->transaksiPenjualan?->sph?->letterSetting) {
            return $this->transaksiPenjualan->sph->letterSetting->locale;
        }

        // Priority 3: Default setting
        $default = LetterSetting::where('is_default', true)->first();
        if ($default) {
            return $default->locale;
        }

        // Fallback
        return 'id';
    }

    /**
     * Format currency based on invoice locale
     *
     * @param float|int $amount
     * @param bool $convertCurrency
     * @return string
     */
    public function formatCurrency($amount, bool $convertCurrency = true): string
    {
        return \App\Services\CurrencyService::formatCurrency($amount, $this->getLocale(), $convertCurrency);
    }

    /**
     * Get currency info for templates
     *
     * @param float|int $amount
     * @param bool $convertCurrency
     * @return array
     */
    public function getCurrencyInfo($amount, bool $convertCurrency = true): array
    {
        return \App\Services\CurrencyService::formatForTemplate($amount, $this->getLocale(), $convertCurrency);
    }



    /**
     * Get the customer from the related sales transaction.
     */
    public function pelanggan()
    {
        return $this->hasOneThrough(
            \App\Models\Pelanggan::class,
            \App\Models\TransaksiPenjualan::class,
            'id', // Foreign key on TransaksiPenjualan table
            'id', // Foreign key on Pelanggan table
            'id_transaksi_penjualan', // Local key on Invoice table
            'id_pelanggan' // Local key on TransaksiPenjualan table
        );
    }

    // Add accessor methods for compatibility with PDF template
    public function getNomorInvoiceAttribute()
    {
        return $this->attributes['nomor_invoice'] ?? null;
    }

    public function getNamaPelangganAttribute()
    {
        return $this->transaksiPenjualan?->pelanggan?->nama ?? 'Unknown Customer';
    }

    /**
     * Calculate sisa tagihan based on total invoice and total terbayar
     */
    public function calculateSisaTagihan(): float
    {
        return $this->total_invoice - $this->total_terbayar;
    }

    /**
     * Update sisa tagihan field
     */
    public function updateSisaTagihan(): void
    {
        $this->sisa_tagihan = $this->calculateSisaTagihan();
        $this->save();
    }

    /**
     * Check if invoice is fully paid
     */
    public function isFullyPaid(): bool
    {
        return $this->sisa_tagihan <= 0;
    }

    /**
     * Check if invoice is overdue
     */
    public function isOverdue(): bool
    {
        return $this->tanggal_jatuh_tempo < now() && !$this->isFullyPaid();
    }

    /**
     * Get delivery orders URL for navigation
     */
    public function getDeliveryOrdersUrlAttribute()
    {
        if ($this->transaksiPenjualan) {
            return route('filament.admin.resources.transaksi-penjualans.view', [
                'record' => $this->transaksiPenjualan->id,
                'activeRelationManager' => 'deliveryOrders'
            ]);
        }
        return null;
    }

    /**
     * Get calculated total invoice amount
     */
    public function getCalculatedTotalAttribute()
    {
        $subtotal = $this->subtotal ?? $this->total_amount ?? 0;
        $pajak = $this->include_ppn ? ($this->total_pajak ?? ($subtotal * 0.11)) : 0;
        $operasional = $this->include_operasional_kerja ? ($this->biaya_operasional_kerja ?? 0) : 0;
        $pbbkb = $this->include_pbbkb ? ($this->biaya_pbbkb ?? 0) : 0;

        return $subtotal + $pajak + $operasional + $pbbkb;
    }

    /**
     * Create journal entry using posting rules when invoice is created
     */
    public function createJournalEntry(): ?Journal
    {
        if ($this->journal_id) {
            return $this->journal; // Journal already exists
        }

        try {
            $journalingService = new JournalingService();
            $journalingService->postTransaction('Invoice', $this);

            // Find the created journal
            $journal = Journal::where('source_type', 'Invoice')
                ->where('source_id', $this->id)
                ->latest()
                ->first();

            if ($journal) {
                $this->update(['journal_id' => $journal->id]);
                return $journal;
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Failed to create journal entry for invoice', [
                'invoice_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get transaction amount for posting rules
     */
    public function getTransactionAmount(): float
    {
        return (float) $this->getTotalInvoiceAttribute();
    }

    /**
     * Get transaction date for posting rules
     */
    public function getTransactionDate(): \Carbon\Carbon
    {
        return $this->tanggal_invoice ?? $this->created_at;
    }

    /**
     * Get transaction code for posting rules
     */
    public function getTransactionCode(): string
    {
        return $this->nomor_invoice;
    }

    /**
     * Boot method for auto-generating invoice number
     */
    protected static function booted(): void
    {
        static::creating(function (Invoice $invoice) {
            // Auto-generate invoice number if not provided
            if (empty($invoice->nomor_invoice)) {
                $numberingService = resolve(\App\Services\NumberingService::class);
                $invoice->nomor_invoice = $numberingService->generateNumber('invoice');
            }
        });
    }
}
