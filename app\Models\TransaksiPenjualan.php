<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Services\MessageService;
use App\Models\NotificationSetting;
use Illuminate\Support\Facades\Log;

class TransaksiPenjualan extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    protected $table = 'transaksi_penjualan';

    protected $fillable = [
        'kode',
        'tipe',
        'tanggal',
        'id_pelanggan',
        'id_alamat_pelanggan',
        'nomor_po',
        'nomor_sph',
        'sph_id',
        'data_dp',
        'top_pembayaran',
        'id_tbbm',
        'id_akun_pendapatan',
        'id_akun_piutang',
        'status',
        'created_by',
        'status'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'tanggal' => 'datetime',
        'data_dp' => 'decimal:2',
    ];

    public function pelanggan()
    {
        return $this->belongsTo(Pelanggan::class, 'id_pelanggan');
    }

    public function alamatPelanggan()
    {
        return $this->belongsTo(AlamatPelanggan::class, 'id_alamat_pelanggan');
    }

    public function tbbm()
    {
        return $this->belongsTo(Tbbm::class, 'id_tbbm');
    }

    public function penjualanDetails()
    {
        return $this->hasMany(PenjualanDetail::class, 'id_transaksi_penjualan');
    }

    // public function deliveryOrder()
    // {
    //     return $this->hasOne(DeliveryOrder::class, 'id_transaksi');
    // }

    public function deliveryOrders()
    {
        return $this->hasMany(DeliveryOrder::class, 'id_transaksi');
    }

    public function fakturPajak()
    {
        return $this->hasOne(FakturPajak::class, 'id_transaksi_penjualan');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function akunPendapatan()
    {
        return $this->belongsTo(Akun::class, 'id_akun_pendapatan');
    }

    public function akunPiutang()
    {
        return $this->belongsTo(Akun::class, 'id_akun_piutang');
    }

    public function sph()
    {
        return $this->belongsTo(Sph::class, 'sph_id');
    }

    /**
     * Register media collections for the TransaksiPenjualan model
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('dokumen_sph')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);

        $this->addMediaCollection('dokumen_dp')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);

        $this->addMediaCollection('dokumen_po')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);
    }

    /**
     * Register media conversions for the TransaksiPenjualan model
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->sharpen(10)
            ->performOnCollections('dokumen_sph', 'dokumen_dp', 'dokumen_po');

        $this->addMediaConversion('preview')
            ->width(300)
            ->height(300)
            ->performOnCollections('dokumen_sph', 'dokumen_dp', 'dokumen_po');
    }

    public function getDokumenSphUrlAttribute()
    {
        return $this->getFirstMediaUrl('dokumen_sph');
    }

    public function getDokumenDpUrlAttribute()
    {
        return $this->getFirstMediaUrl('dokumen_dp');
    }

    public function getDokumenPoUrlAttribute()
    {
        return $this->getFirstMediaUrl('dokumen_po');
    }

    /**
     * Get the invoices associated with the sales transaction.
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'id_transaksi');
    }

    /**
     * Get the receipts associated with the sales transaction.
     */
    public function receipts()
    {
        return $this->hasMany(Receipt::class, 'id_transaksi');
    }

    /**
     * Get the tax invoices associated with the sales transaction.
     */
    public function taxInvoices()
    {
        return $this->hasMany(TaxInvoice::class, 'id_transaksi');
    }

    // delivery order
    public function getDeliveryOrderUrlAttribute()
    {
        return $this->deliveryOrder ? route('filament.admin.resources.delivery-orders.view', ['record' => $this->deliveryOrder->id]) : null;
    }

    public function approvals(): HasMany
    {
        return $this->hasMany(TransaksiPenjualanApproval::class, 'id_transaksi_penjualan')->latest();
    }

    protected static function booted(): void
    {
        static::creating(function (TransaksiPenjualan $transaksi) {
            // Set default "-" if kode is empty
            if (empty($transaksi->kode)) {
                $transaksi->kode = '-';
            }
        });

        static::updating(function (TransaksiPenjualan $transaksi) {
            // Set default "-" if kode is empty during update
            if (empty($transaksi->kode)) {
                $transaksi->kode = '-';
            }
        });

        static::created(function (TransaksiPenjualan $transaksi) {
            // Send notification for new transaction
            self::sendPenjualanNotification($transaksi, 'penjualan_baru');
        });

        static::updated(function (TransaksiPenjualan $transaksi) {
            // Send notification for updated transaction (only if not approved)
            if ($transaksi->status !== 'approved') {
                self::sendPenjualanNotification($transaksi, 'penjualan_baru');
            }
        });
    }

    /**
     * Send WhatsApp notification for penjualan events
     */
    private static function sendPenjualanNotification(TransaksiPenjualan $transaksi, string $eventName): void
    {
        try {
            // 1. Ambil instance MessageService dari container
            $messageService = resolve(MessageService::class);

            // 2. Cari pengaturan notifikasi dari database untuk event ini
            $notifSettings = NotificationSetting::with('user')
                ->where('event_name', $eventName)
                ->where('is_active', true)
                ->get();

            // 3. Jika tidak ada aturan yang ditemukan, hentikan proses
            if ($notifSettings->isEmpty()) {
                Log::info("sendPenjualanNotification(): No active notification settings found for event '{$eventName}' on Transaksi ID: {$transaksi->id}.");
                return;
            }

            // 4. Kirim notifikasi berdasarkan setiap aturan yang ditemukan
            foreach ($notifSettings as $notifSetting) {
                // Akses user melalui relasi yang sudah di-load, dan pastikan nomor HP ada
                if ($notifSetting->user && !empty($notifSetting->user->hp)) {
                    $messageService->sendNewPenjualanNotification(
                        $transaksi,
                        $notifSetting->user->hp
                    );

                    Log::info("WhatsApp notification sent for Transaksi ID: {$transaksi->id} to {$notifSetting->user->name} ({$notifSetting->user->hp})");
                }
            }
        } catch (\Exception $e) {
            Log::error("Failed to send WhatsApp notification for Transaksi ID: {$transaksi->id}. Error: " . $e->getMessage());
        }
    }

    /**
     * Get formatted transaction number for display
     */
    public function getNomorTransaksiAttribute(): string
    {
        return 'TXN-' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get display identifier (use transaction number instead of SO number)
     */
    public function getDisplayIdentifierAttribute(): string
    {
        return $this->nomor_transaksi;
    }
}
