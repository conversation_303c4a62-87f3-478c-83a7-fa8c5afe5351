<?php

namespace App\Filament\Resources\TaxInvoiceResource\Pages;

use App\Filament\Resources\TaxInvoiceResource;
use App\Models\TaxInvoice;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Barryvdh\DomPDF\Facade\Pdf;

class ViewTaxInvoice extends ViewRecord
{
    protected static string $resource = TaxInvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('Preview Tax Invoice')
                ->color('gray')
                ->icon('heroicon-o-eye')
                ->action(null)
                ->modalContent(function (TaxInvoice $record): \Illuminate\View\View {
                    // Load the record with necessary relationships
                    $record->load([
                        'invoice.transaksiPenjualan.pelanggan',
                        'deliveryOrder',
                        'transaksiPenjualan',
                        'createdBy.jabatan'
                    ]);

                    return View::make('tax_invoice.tax-invoice-preview', ['record' => $record]);
                })
                ->modalHeading("Preview: {$this->record->nomor_tax_invoice}")
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Tutup')
                ->slideOver()
                ->modalWidth('4xl'),

            Actions\Action::make('download_pdf')
                ->label('Download PDF')
                ->color('success')
                ->icon('heroicon-o-arrow-down-tray')
                ->action(function () {
                    try {
                        // Load the tax invoice with all necessary relationships
                        $taxInvoice = TaxInvoice::with([
                            'invoice.transaksiPenjualan.pelanggan',
                            'deliveryOrder',
                            'transaksiPenjualan',
                            'createdBy.jabatan'
                        ])->find($this->record->id);

                        if (!$taxInvoice) {
                            throw new \Exception('Tax Invoice not found');
                        }

                        // Generate dynamic filename
                        $filename = 'TaxInvoice_' . str_replace(['/', '\\', ' '], '_', $taxInvoice->nomor_tax_invoice) . '_' . now()->format('Ymd_His') . '.pdf';

                        // Get logo as base64
                        $logoPath = public_path('images/lrp.png');
                        $logoBase64 = '';

                        if (File::exists($logoPath)) {
                            $logoBase64 = base64_encode(File::get($logoPath));
                        }

                        // Load the PDF view with the record data
                        $pdf = Pdf::loadView('pdf.tax_invoice', [
                            'record' => $taxInvoice,
                            'logoBase64' => $logoBase64
                        ])
                            ->setPaper('a4', 'portrait')
                            ->setOptions([
                                'isHtml5ParserEnabled' => true,
                                'isPhpEnabled' => true,
                                'defaultFont' => 'Arial',
                                'dpi' => 150,
                                'defaultPaperSize' => 'a4',
                                'chroot' => public_path(),
                            ]);

                        // Stream the PDF as a download
                        return response()->streamDownload(function () use ($pdf) {
                            echo $pdf->output();
                        }, $filename, [
                            'Content-Type' => 'application/pdf',
                            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
                        ]);
                    } catch (\Exception $e) {
                        // Log the error for debugging
                        Log::error('Failed to generate Tax Invoice PDF: ' . $e->getMessage());
                        Log::error('Tax Invoice PDF Error Stack Trace: ' . $e->getTraceAsString());
                        Log::error('Tax Invoice PDF Error Context: ', [
                            'tax_invoice_id' => $this->record->id,
                            'user_id' => Auth::id(),
                        ]);

                        // Show notification to user
                        \Filament\Notifications\Notification::make()
                            ->title('Error generating PDF')
                            ->body('Failed to generate PDF: ' . $e->getMessage())
                            ->danger()
                            ->send();

                        return;
                    }
                }),

            Actions\EditAction::make(),
        ];
    }
}
