<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key to akun (Chart of Accounts) if it doesn't exist
        if (!Schema::hasColumn('payment_methods', 'akun_id')) {
            Schema::table('payment_methods', function (Blueprint $table) {
                $table->foreignId('akun_id')
                    ->nullable()
                    ->after('method_display_name')
                    ->constrained('akun')
                    ->nullOnDelete();
            });
        }

        // Create default COA entries for payment methods if they don't exist
        $this->createDefaultCOAEntries();

        // Update existing payment methods with appropriate COA mappings
        $this->mapExistingPaymentMethodsToCOA();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_methods', function (Blueprint $table) {
            $table->dropForeign(['akun_id']);
            $table->dropColumn('akun_id');
        });
    }

    /**
     * Create default Chart of Accounts entries for payment methods
     */
    private function createDefaultCOAEntries(): void
    {
        $defaultAccounts = [
            // Cash Accounts
            [
                'kode_akun' => '1110',
                'nama_akun' => 'Kas di Tangan',
                'kategori_akun' => 'Aset',
                'tipe_akun' => 'Debit',
                'saldo_awal' => 0,
            ],
            [
                'kode_akun' => '1111',
                'nama_akun' => 'Kas Kecil',
                'kategori_akun' => 'Aset',
                'tipe_akun' => 'Debit',
                'saldo_awal' => 0,
            ],

            // Bank Accounts
            [
                'kode_akun' => '1120',
                'nama_akun' => 'Bank BNI',
                'kategori_akun' => 'Aset',
                'tipe_akun' => 'Debit',
                'saldo_awal' => 0,
            ],
            [
                'kode_akun' => '1121',
                'nama_akun' => 'Bank Mandiri',
                'kategori_akun' => 'Aset',
                'tipe_akun' => 'Debit',
                'saldo_awal' => 0,
            ],
            [
                'kode_akun' => '1122',
                'nama_akun' => 'Bank BCA',
                'kategori_akun' => 'Aset',
                'tipe_akun' => 'Debit',
                'saldo_awal' => 0,
            ],
            [
                'kode_akun' => '1123',
                'nama_akun' => 'Bank BRI',
                'kategori_akun' => 'Aset',
                'tipe_akun' => 'Debit',
                'saldo_awal' => 0,
            ],

            // Other Payment Methods
            [
                'kode_akun' => '1130',
                'nama_akun' => 'Piutang Cek',
                'kategori_akun' => 'Aset',
                'tipe_akun' => 'Debit',
                'saldo_awal' => 0,
            ],
            [
                'kode_akun' => '1131',
                'nama_akun' => 'Piutang Giro',
                'kategori_akun' => 'Aset',
                'tipe_akun' => 'Debit',
                'saldo_awal' => 0,
            ],
            [
                'kode_akun' => '1140',
                'nama_akun' => 'E-Wallet',
                'kategori_akun' => 'Aset',
                'tipe_akun' => 'Debit',
                'saldo_awal' => 0,
            ],
        ];

        foreach ($defaultAccounts as $account) {
            $existing = DB::table('akun')->where('kode_akun', $account['kode_akun'])->first();
            if (!$existing) {
                DB::table('akun')->insert($account);
            }
        }
    }

    /**
     * Map existing payment methods to appropriate COA accounts
     */
    private function mapExistingPaymentMethodsToCOA(): void
    {
        $paymentMethods = DB::table('payment_methods')->get();

        foreach ($paymentMethods as $method) {
            $akunId = null;

            // Map based on method type and bank name
            switch ($method->method_name) {
                case 'cash':
                    if (str_contains(strtolower($method->method_display_name ?? ''), 'kecil')) {
                        $akunId = DB::table('akun')->where('kode_akun', '1111')->value('id'); // Kas Kecil
                    } else {
                        $akunId = DB::table('akun')->where('kode_akun', '1110')->value('id'); // Kas di Tangan
                    }
                    break;

                case 'bank_transfer':
                    $bankName = strtolower($method->bank_name ?? '');
                    if (str_contains($bankName, 'bni')) {
                        $akunId = DB::table('akun')->where('kode_akun', '1120')->value('id');
                    } elseif (str_contains($bankName, 'mandiri')) {
                        $akunId = DB::table('akun')->where('kode_akun', '1121')->value('id');
                    } elseif (str_contains($bankName, 'bca')) {
                        $akunId = DB::table('akun')->where('kode_akun', '1122')->value('id');
                    } elseif (str_contains($bankName, 'bri')) {
                        $akunId = DB::table('akun')->where('kode_akun', '1123')->value('id');
                    } else {
                        $akunId = DB::table('akun')->where('kode_akun', '1120')->value('id'); // Default to BNI
                    }
                    break;

                case 'check':
                    $akunId = DB::table('akun')->where('kode_akun', '1130')->value('id');
                    break;

                case 'giro':
                    $akunId = DB::table('akun')->where('kode_akun', '1131')->value('id');
                    break;

                case 'e_wallet':
                case 'virtual_account':
                    $akunId = DB::table('akun')->where('kode_akun', '1140')->value('id');
                    break;

                default:
                    // For legacy 'bank' entries or other types, try to map by bank name
                    $bankName = strtolower($method->bank_name ?? '');
                    if (str_contains($bankName, 'bni')) {
                        $akunId = DB::table('akun')->where('kode_akun', '1120')->value('id');
                    } elseif (str_contains($bankName, 'mandiri')) {
                        $akunId = DB::table('akun')->where('kode_akun', '1121')->value('id');
                    } elseif (str_contains($bankName, 'bca')) {
                        $akunId = DB::table('akun')->where('kode_akun', '1122')->value('id');
                    } elseif (str_contains($bankName, 'bri')) {
                        $akunId = DB::table('akun')->where('kode_akun', '1123')->value('id');
                    } else {
                        $akunId = DB::table('akun')->where('kode_akun', '1110')->value('id'); // Default to cash
                    }
                    break;
            }

            if ($akunId) {
                DB::table('payment_methods')
                    ->where('id', $method->id)
                    ->update(['akun_id' => $akunId]);
            }
        }
    }
};
